---
type: "manual"
---

你的任务是，从入口代码开始分析出完整的代码调用链。不仅仅分析入口文件本身，请依序追踪调用链涉及到的代码文件、配置文件，输出其核心业务逻辑的**调用链信息**。调用链由节点组成，一个调用节点可以是codebase中的一个函数、一个类的方法。

由于涉及代码较多，请你对照下述产出结构，拆分成多个步骤逐步执行。
错误做法：将调用链涉及到的所有文件分析出结果，才将结果写入文档。
正确做法：每分析完一个节点，立刻调用工具将结果写入文档。

你必须在每个步骤完成之后**复述产出要求**，因为你总是忘记产出要求。你的复述以“我将在每个步骤完成之后复述产出要求：”开头。

# 产出要求 

请按照以下结构，使用Markdown格式生成详细的调用链说明。
## 调用链

逐一为每个节点提供以下信息，**仅关注重要的函数/方法调用**：
### 节点(以函数/类方法命名节点)
所在代码文件相对路径
用途
逐一说明输入参数
输出说明

## 整体用途
详述调用链的整体用途

## 目录结构
调用链涉及到的文件及其所属的目录结构

## 调用时序图
生成mermaid sequenceDiagram格式的时序图。participant为**文件相对路径**，展示为完成一个典型请求，以及调用时传递的参数和返回值。