import os
import cv2
import numpy as np

bbox = [880, 1130, 1209, 1184, 
        663,  451, 1513, 499,
        650, 2143, 1421, 2249,
        641, 1809, 1437, 1915,
        587,  385, 1511, 436 ]

img_path = r"D:\datasets\DLA\Bench\images\104.png"
img = cv2.imread(img_path)
bboxes = np.asarray(bbox).reshape(-1, 4)
cnt = 1
for bbox in bboxes:
    print(bbox)
    x1, y1, x2, y2 = bbox
    img_crop = img[y1:y2, x1:x2, :]
    print(img_crop.shape)
    cv2.imwrite(f"E:\codes\OCR\Formula\crop\{cnt:04d}.png", img_crop)
    cnt += 1
