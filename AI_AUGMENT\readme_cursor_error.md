# 任务失败原因与改进建议总结

## 1. 为什么没有完成“每一行代码解读”而输出了函数级粗略总结？

### 主要原因分析

1. **默认粒度偏差**  
   默认以“函数/方法”为最小分析单元，未明确“逐行”或“每一行”分析，导致未覆盖每一行实现细节。
2. **对“处理方式”理解的偏差**  
   理解为“函数用途”而非“每一行的case-by-case处理说明”，未将正则、分支、特殊case等细致拆解。
3. **未充分利用规则**  
   虽然有 `@parsecode.md`，但未严格执行“每个处理步骤都要立即产出、每一行都要分析”的要求。
4. **流程图/可视化偏向宏观**  
   输出流程图偏向“函数调用关系”，未细化到“每一行代码的处理分支和case流转”。

---

## 2. 经验总结与改进建议

- **明确粒度**：任务开始时必须明确“分析粒度”，如需“每一行”，则每一行都要有注释、说明、case示例。
- **case-by-case 拆解**：字符串处理、正则、分支等要将每一种case、每一个if/else、每一个正则的作用和输入输出单独列出。
- **严格遵守规则**：如有 `@parsecode.md`，每一步都要复述产出要求，确保不遗漏细节。
- **可视化流程**：流程图要细化到分支、特殊case、正则处理等细节流转。
- **及时产出**：每分析完一行/一个case，立即写入文档，避免遗漏。

---

## 3. 下次任务的推荐提示词

你可以直接用如下提示词来确保AI严格执行你的目标：

---

### 推荐提示词

> 你需要对 `xxx.py` 文件进行**逐行代码解读**，分析每一行代码的作用、处理的latex字符串case、输入输出变化、正则/分支/特殊情况的处理方式。
> 
> - 每一行都要有详细注释和说明，不能只总结函数级别的功能。
> - 对于每一个正则、每一个if/else、每一个字符串处理，都要举例说明“处理前/处理后”字符串。
> - 每分析完一行或一个处理case，立即将结果写入指定文档，不要等到全部分析完再总结。
> - 输出内容要包括：  
>   1. 该行代码的功能说明  
>   2. 处理的latex字符串case（举例）  
>   3. 输入输出变化（举例）  
>   4. 该行在整体流程中的作用  
> - 最后，输出一份详细的流程图（mermaid），每一个处理case都要在流程图中体现，便于直观对比和查找潜在冲突。
> - 严格按照上述要求执行，不能只输出函数摘要或主流程。
> 
> 产出格式：  
> - Markdown，每一行代码一个小节  
> - 每个case举例用“处理前：... 处理后：...”  
> - 流程图用mermaid graph LR/TD  
> 
> 目标：**彻底还原和解读latex字符串的每一步处理方式，便于后续对比和优化。**

---

## 4. 结论

本次任务未能达成目标，主要是因为分析粒度、case-by-case说明和流程可视化不够细致。  
下次请直接用上述提示词，AI会严格按照“每一行代码、每一个case、每一个处理分支”进行详细解读和可视化，确保满足你的需求。

如需进一步细化或有其他要求，欢迎随时补充！ 