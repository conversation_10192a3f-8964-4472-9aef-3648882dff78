# -*- coding: utf-8 -*-
# """
# 图像去重工具
# 
# 本脚本提供两大核心功能：
# 1.  在单个数据集中查找重复或高度相似的图片 (internal_check)。
# 2.  在两个数据集之间查找重复或高度相似的图片 (cross_check)。
# 
# 它使用感知哈希算法来判断图像相似度，并为长时间运行的任务提供断点续传支持。
# 
# 作者: Cascade (AI 智能体)
# 日期: 2025-07-17
# 设计文档: d:\formula_dataset\cop_readme\readme_lld.md
# """

import os
import sys
import csv
import logging
import time
from datetime import datetime
from PIL import Image
import imagehash
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import pandas as pd

# ==============================================================================
# 用户配置区域
# ==============================================================================
CONFIG = {
    # --- 全局运行模式 ---
    # "cross_check": 跨数据集比较。比较 'MAIN_IMAGE_DIR' 和 'REFERENCE_IMAGE_DIR' 中的图片。
    # "internal_check": 数据集内部自查。查找 'TARGET_DIR' 内部的重复图片。
    "MODE": "internal_check",

    # --- 通用设置 (两种模式均生效) ---
    "COMMON_SETTINGS": {
        # 日志文件的完整路径。脚本运行过程中的所有信息、警告和错误都将记录于此。
        "LOG_FILE_PATH": r"C:\dataset\consolidated_data_v1_log\consolidated_data.log",
        
        # 使用的CPU核心数。0 表示使用所有可用的核心以最大化速度；1 表示禁用多进程（单核运行），便于调试。
        "NUM_PROCESSES": 0,
        
        # --- 哈希算法相关设置 ---
        "HASH_SETTINGS": {
            # 感知哈希的尺寸。对于phash，生成的哈希位数为 hash_size * hash_size。
        # 8x8 -> 64位哈希 (标准phash)
        # 16x16 -> 256位哈希 (更高精度，但计算更慢，且需要调整LSH参数)
        "HASH_SIZE": 8, # <-- 已从16修正为8，确保生成64位哈希
        },
        
        # 允许处理的图片文件扩展名列表。脚本会忽略不在此列表中的文件。
        "ALLOWED_EXTENSIONS": (".png", ".jpg", ".jpeg", ".bmp"),
    },

    # --- "cross_check" 模式专属设置 ---
    "CROSS_CHECK_SETTINGS": {
        # 主数据集的文件夹路径。
        "MAIN_IMAGE_DIR": r"C:\dataset\consolidated_data\images",
        # 参考数据集的文件夹路径。
        "REFERENCE_IMAGE_DIR": r"D:\formula_dataset_only\crohme-full\images",
        # 输出的CSV报告文件的完整路径。
        "REPORT_FILE_PATH": r"D:\formula_dataset\remove_crohme\cross_check_report.csv",
        # 断点续传进度文件的路径。用于记录主数据集中已处理过的图片。
        "PROGRESS_FILE_PATH": r"D:\formula_dataset\logs\remove_crohme.log",
        # 相似度阈值（汉明距离）。两个哈希值的差异小于或等于此值时，被认为是相似图片。值越小，要求越高。60是一个比较宽松的阈值。
        "SIMILARITY_THRESHOLD": 60,
    },

    # --- "internal_check" 模式专属设置 ---
    "INTERNAL_CHECK_SETTINGS": {
        # 需要进行内部去重的数据集文件夹路径。
        # "TARGET_DIR": r"C:\dataset\consolidated_data\images",
        "TARGET_DIR": r"C:\dataset\duplicates_backup_crohme\images",
        # 输出的CSV报告文件的完整路径。
        "REPORT_FILE_PATH": r"C:\dataset\consolidated_data_v1_log\internal_check_report.csv",
        # 断点续传进度文件的路径。用于记录已完成外层循环比较的图片。
        "PROGRESS_FILE_PATH": r"C:\dataset\consolidated_data_v1_log\internal_check_progress.log",
        # 相似度阈值（汉明距离）。规则同上。
        "SIMILARITY_THRESHOLD": 20, # <-- 注意：LSH对高阈值非常敏感，建议从一个较小的值开始，例如 3-6

        # --- LSH (Locality-Sensitive Hashing) 相关设置 ---
        "LSH_SETTINGS": {
            # LSH的分组数量 (bands)。这是LSH的核心参数之一。
            # 增加bands会提高查准率（减少误报），但可能降低查全率（增加漏报）。
            "NUM_BANDS": 16,
            # 每个分组中的行数 (rows per band)。
            # HASH_SIZE * 4 必须等于 NUM_BANDS * ROWS_PER_BAND。
            # 例如，HASH_SIZE=16 -> 16*4=64位。如果NUM_BANDS=16，则ROWS_PER_BAND必须为4。
            "ROWS_PER_BAND": 4,
        },
    }
}

# ==============================================================================
# HELPER FUNCTIONS
# ==============================================================================

def setup_logging(log_path):
    """初始化日志记录器，同时输出到控制台和文件。"""
    log_dir = os.path.dirname(log_path)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, mode='a', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def calculate_hash_and_info(image_path, hash_size):
    """
    为单个图片计算感知哈希值并获取其尺寸。
    这是性能优化的关键：一次磁盘读取，完成所有信息获取。
    """
    try:
        with Image.open(image_path) as img:
            # 转换为 L（灰度）模式以处理各种图像格式，如 RGBA, P 等。
            hash_val = imagehash.phash(img.convert('L'), hash_size=hash_size)
            # 同时返回哈希值和尺寸信息
            return image_path, (hash_val, img.size)
    except Exception as e:
        # logging.warning(f"Could not process {image_path}: {e}")
        return image_path, None

def calculate_all_image_info_parallel(image_paths, hash_size, num_processes):
    """
    并行计算图片列表的哈希值和尺寸。
    返回一个列表，包含所有图片的信息。
    """
    desc = "Calculating Hashes & Dimensions"
    if num_processes == 1:
        # 单核模式
        results = [calculate_hash_and_info(p, hash_size) for p in tqdm(image_paths, desc=f"{desc} (Single-core)")]
    else:
        # 多核模式
        pool = Pool(processes=num_processes if num_processes > 0 else cpu_count())
        tasks = [pool.apply_async(calculate_hash_and_info, (p, hash_size)) for p in image_paths]
        
        results = []
        for task in tqdm(tasks, desc=f"{desc} (Multi-core)"):
            results.append(task.get())
        
        pool.close()
        pool.join()

    # 过滤掉处理失败的图片 (返回值为 None)
    # 返回一个列表，格式为: [(path, (hash, (width, height))), ...]
    processed_info = [res for res in results if res and res[1] is not None]
    return processed_info

def scan_files(directory, extensions):
    """递归扫描目录，查找具有指定扩展名的文件。"""
    logging.info(f"Scanning for images in: {directory}")
    image_paths = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(extensions):
                image_paths.append(os.path.join(root, file))
    logging.info(f"Found {len(image_paths)} images.")
    return image_paths

def load_progress(progress_file):
    """从进度文件中加载已处理过的文件路径集合。"""
    if not os.path.exists(progress_file):
        return set()
    with open(progress_file, 'r', encoding='utf-8') as f:
        processed = {line.strip() for line in f if line.strip()}
    logging.info(f"Loaded {len(processed)} processed items from {progress_file}")
    return processed

def append_to_progress_log(progress_file, item):
    """将单个已处理项追加到进度日志中。"""
    with open(progress_file, 'a', encoding='utf-8') as f:
        f.write(f"{item}\n")

def get_timestamped_path(file_path):
    """如果文件已存在，则为其附加时间戳。"""
    if not os.path.exists(file_path):
        return file_path
    base, ext = os.path.splitext(file_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{base}_{timestamp}{ext}"
    logging.warning(f"Report file exists. Writing to new file: {new_path}")
    return new_path

def write_report_header(writer, config):
    """将配置元数据作为注释行写入CSV文件的头部。"""
    writer.writerow([f"# Report generated on: {datetime.now().isoformat()}"])
    writer.writerow([f"# Mode: {config['MODE']}"])
    for key, value in config['COMMON_SETTINGS'].items():
        writer.writerow([f"# Common.{key}: {value}"])
    active_settings = config[f"{config['MODE'].upper()}_SETTINGS"]
    for key, value in active_settings.items():
        writer.writerow([f"# {config['MODE']}.{key}: {value}"])

# ==============================================================================
# CORE LOGIC: INTERNAL CHECK (Optimized with LSH)
# ==============================================================================

def hamming_distance(h1, h2):
    """计算两个ImageHash对象之间的汉明距离。"""
    # ImageHash对象重载了减法操作符来计算汉明距离
    return h1 - h2

def build_lsh_index(hash_to_info_map, lsh_settings):
    """
    构建LSH索引（分桶）。

    Args:
        hash_to_info_map (dict): 哈希字符串到图片信息列表的映射。
        lsh_settings (dict): LSH相关参数。

    Returns:
        dict: LSH索引，结构为 {band_index: {bucket_hash: [hash_str_1, hash_str_2, ...]}}。
    """
    num_bands = lsh_settings['NUM_BANDS']
    rows_per_band = lsh_settings['ROWS_PER_BAND']
    hash_size = num_bands * rows_per_band

    logging.info(f"Building LSH index with {num_bands} bands and {rows_per_band} rows per band.")
    
    lsh_index = {i: {} for i in range(num_bands)}
    
    for hash_str in tqdm(hash_to_info_map.keys(), desc="Building LSH Buckets"):
        # 确保哈希长度正确
        if len(hash_str) * 4 != hash_size:
            # logging.warning(f"Skipping hash {hash_str} with incorrect length.")
            continue

        for i in range(num_bands):
            start = i * rows_per_band
            end = start + rows_per_band
            # 从16进制字符串中切片作为bucket的哈希
            band_hash = hash_str[start:end]
            
            # 将原始的完整哈希字符串放入对应的桶中
            if band_hash not in lsh_index[i]:
                lsh_index[i][band_hash] = []
            lsh_index[i][band_hash].append(hash_str)
            
    return lsh_index

def find_candidate_pairs(lsh_index):
    """
    从LSH索引中找出所有可能重复的图片对（候选对）。

    Args:
        lsh_index (dict): LSH索引。

    Returns:
        set: 一个包含所有候选对的集合，对的形式为 (hash_str1, hash_str2)，且已排序去重。
    """
    logging.info("Finding candidate pairs from LSH buckets...")
    candidate_pairs = set()
    for band_id, buckets in tqdm(lsh_index.items(), desc="Generating Candidate Pairs"):
        for bucket_hash, hash_list in buckets.items():
            if len(hash_list) > 1:
                # 桶内所有哈希两两组合，形成候选对
                for i in range(len(hash_list)):
                    for j in range(i + 1, len(hash_list)):
                        # 排序以确保(h1, h2)和(h2, h1)被视为相同
                        h1 = hash_list[i]
                        h2 = hash_list[j]
                        if h1 != h2:
                            pair = tuple(sorted((h1, h2)))
                            candidate_pairs.add(pair)
    
    logging.info(f"Found {len(candidate_pairs)} unique candidate pairs.")
    return candidate_pairs

def verify_candidates_worker(args):
    """
    并行工作函数：验证候选对的汉明距离。
    """
    pairs_chunk, threshold, hash_to_info_map = args
    results = []
    for h1_str, h2_str in pairs_chunk:
        h1 = imagehash.hex_to_hash(h1_str)
        h2 = imagehash.hex_to_hash(h2_str)
        
        dist = hamming_distance(h1, h2)
        
        if dist <= threshold:
            # 如果距离满足，则从主信息map中获取所有关联的图片进行组合
            for path1, size1 in hash_to_info_map[h1_str]:
                for path2, size2 in hash_to_info_map[h2_str]:
                    dims1_str = f"{size1[0]}x{size1[1]}"
                    dims2_str = f"{size2[0]}x{size2[1]}"
                    # 规范化对的顺序，避免 (A,B) 和 (B,A) 都被记录
                    if path1 < path2:
                        results.append([path1, dims1_str, path2, dims2_str, dist])
    return results

def run_internal_check(config):
    """在单个目录中查找重复项 (使用LSH进行高效候选集筛选)。"""
    cfg = config['INTERNAL_CHECK_SETTINGS']
    common_cfg = config['COMMON_SETTINGS']

    if not os.path.isdir(cfg['TARGET_DIR']):
        logging.error(f"Target directory does not exist: {cfg['TARGET_DIR']}")
        return

    report_path = get_timestamped_path(cfg['REPORT_FILE_PATH'])
    all_image_paths = scan_files(cfg['TARGET_DIR'], common_cfg['ALLOWED_EXTENSIONS'])
    if not all_image_paths:
        logging.warning("No images found to process.")
        return

    logging.info(f"Total images to process: {len(all_image_paths)}")

    # 步骤1: 并行计算所有图片的哈希值和尺寸 (与旧版相同)
    all_image_data = calculate_all_image_info_parallel(
        all_image_paths,
        common_cfg['HASH_SETTINGS']['HASH_SIZE'],
        common_cfg['NUM_PROCESSES']
    )
    if not all_image_data:
        logging.error("Failed to calculate any image hashes. Aborting.")
        return

    # 步骤2: 构建哈希到图片信息(路径, 尺寸)的映射 (与旧版相同)
    logging.info("Building hash-to-info map...")
    hash_to_info_map = {}
    for path, (hash_val, size) in tqdm(all_image_data, desc="Building Hash Map"):
        hash_str = str(hash_val)
        if hash_str not in hash_to_info_map:
            hash_to_info_map[hash_str] = []
        hash_to_info_map[hash_str].append((path, size))
    logging.info(f"Built map with {len(hash_to_info_map)} unique hashes.")

    # --- LSH 核心流程开始 ---
    
    # 步骤3: 构建LSH索引 (分桶)
    lsh_settings = cfg['LSH_SETTINGS']
    # 动态计算HASH_SIZE以确保其与LSH参数匹配
    lsh_hash_size = lsh_settings['NUM_BANDS'] * lsh_settings['ROWS_PER_BAND']
    # 验证哈希位数是否匹配LSH设置
    total_hash_bits = common_cfg['HASH_SETTINGS']['HASH_SIZE'] * common_cfg['HASH_SETTINGS']['HASH_SIZE']
    if total_hash_bits != lsh_hash_size:
        logging.error(f"Configuration error: Total hash bits ({total_hash_bits}) from HASH_SIZE ({common_cfg['HASH_SETTINGS']['HASH_SIZE']}^2) does not match LSH total bits ({lsh_hash_size}).")
        return

    lsh_index = build_lsh_index(hash_to_info_map, lsh_settings)

    # 步骤4: 从LSH桶中生成候选对
    candidate_pairs = find_candidate_pairs(lsh_index)
    
    found_duplicates = 0
    with open(report_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        write_report_header(writer, config)
        writer.writerow(['image1_path', 'image1_dims', 'image2_path', 'image2_dims', 'hamming_distance'])

        # 步骤5: 并行验证候选对的汉明距离
        num_processes = common_cfg['NUM_PROCESSES'] if common_cfg['NUM_PROCESSES'] > 0 else cpu_count()
        candidate_list = list(candidate_pairs)
        chunk_size = max(1, len(candidate_list) // (num_processes * 4))
        chunks = [candidate_list[i:i + chunk_size] for i in range(0, len(candidate_list), chunk_size)]
        
        logging.info(f"Verifying {len(candidate_list)} candidate pairs in parallel with {num_processes} processes and {len(chunks)} chunks...")

        args_list = [(chunk, cfg['SIMILARITY_THRESHOLD'], hash_to_info_map) for chunk in chunks]

        pool = Pool(processes=num_processes)
        with tqdm(total=len(chunks), desc="Verifying Candidates") as pbar:
            for result_chunk in pool.imap_unordered(verify_candidates_worker, args_list):
                if result_chunk:
                    writer.writerows(result_chunk)
                    found_duplicates += len(result_chunk)
                pbar.update(1)
                pbar.set_postfix({'found_pairs': found_duplicates})
        
        pool.close()
        pool.join()

        # 步骤6: 处理哈希完全相同的图片组 (与旧版相同)
        logging.info("Processing groups of identical hashes...")
        for h, info_list in tqdm(hash_to_info_map.items(), desc="Processing Identical Hash Groups"):
            if len(info_list) > 1:
                for i in range(len(info_list)):
                    for j in range(i + 1, len(info_list)):
                        path1, size1 = info_list[i]
                        path2, size2 = info_list[j]
                        dims1_str = f"{size1[0]}x{size1[1]}"
                        dims2_str = f"{size2[0]}x{size2[1]}"
                        writer.writerow([path1, dims1_str, path2, dims2_str, 0])
                        found_duplicates += 1

    logging.info(f"Internal check complete. Found {found_duplicates} duplicate pairs. Report saved to {report_path}")



# ==============================================================================
# CORE LOGIC: CROSS CHECK
# ==============================================================================

# (cross_check logic would go here if implemented)

# ==============================================================================
# MAIN EXECUTION BLOCK
# ==============================================================================

if __name__ == "__main__":
    # 1. 设置日志
    # 根据不同的模式使用不同的日志文件路径
    log_path = ""
    mode = CONFIG['MODE']
    if mode == 'cross_check':
        log_path = CONFIG['CROSS_CHECK_SETTINGS']['PROGRESS_FILE_PATH']
    elif mode == 'internal_check':
        log_path = CONFIG['INTERNAL_CHECK_SETTINGS']['PROGRESS_FILE_PATH']
    else:
        # 如果模式未设置或错误，则使用默认日志文件
        log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'default_run.log')
    
    setup_logging(log_path)

    # 2. 记录开始信息
    logging.info("======================================================")
    logging.info(f"Script started in '{mode}' mode.")
    logging.info(f"Using {CONFIG['COMMON_SETTINGS']['NUM_PROCESSES']} processes.")
    logging.info(f"Python executable: {sys.executable}")
    logging.info("======================================================")

    # 3. 根据模式执行不同的功能
    if mode == 'cross_check':
        # run_cross_check(CONFIG)
        logging.warning("'cross_check' mode is defined but not implemented yet.")
    elif mode == 'internal_check':
        run_internal_check(CONFIG)
    else:
        logging.error(f"Invalid mode specified: '{mode}'. Please set MODE to 'cross_check' or 'internal_check'.")

    logging.info("Script finished.")
