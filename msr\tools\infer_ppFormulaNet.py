from paddlex import create_pipeline

pipeline = create_pipeline(pipeline="formula_recognition")

output = pipeline.predict(
        #input="/aipdf-mlp/guofu/datasets/Formula/test_imgs/0001.png",
        input="/aipdf-mlp/guofu/datasets/Formula/test_imgs",
        use_layout_detection=False,
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        layout_threshold=0.5,
        layout_nms=True,
        layout_unclip_ratio=1.0,
        layout_merge_bboxes_mode="large"
)

for res in output:
    """
    print("="*40)
    res.print()
    res.save_to_img(save_path="./output/")
    res.save_to_json(save_path="./output/")
    #"""
    with open("pred_ppFormulaNet_M.txt", 'a') as fa:
        fa.write(res["formula_res_list"][0]["rec_formula"]+"\n")

