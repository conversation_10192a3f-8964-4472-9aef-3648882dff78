# -*- coding:utf-8 -*-
# create: @time: 10/8/23 11:47
import argparse
import glob
import os
import sys
sys.path.append('../')
print(os.getcwd())

import torch
from PIL import Image
from transformers import VisionEncoderDecoderModel
from transformers.models.nougat import NougatTokenizerFast
from nougat_latex.util import process_raw_latex_code
from nougat_latex import NougatLaTexProcessor


def parse_option():
    parser = argparse.ArgumentParser(prog="nougat inference config", description="model archiver")
    parser.add_argument("--pretrained_model_name_or_path", default="Norm/nougat-latex-base")
    parser.add_argument("--img_path", help="path to single latex image or folder containing images", required=True)
    parser.add_argument("--output_file", help="output txt file to save all results", default="./latex_results.txt")
    parser.add_argument("--device", default="gpu")
    return parser.parse_args()


def process_single_image(image_path, model, tokenizer, latex_processor, device):
    """Process a single image and return the latex sequence"""
    try:
        image = Image.open(image_path)
        if not image.mode == "RGB":
            image = image.convert('RGB')

        pixel_values = latex_processor(image, return_tensors="pt").pixel_values
        task_prompt = tokenizer.bos_token
        decoder_input_ids = tokenizer(task_prompt, add_special_tokens=False,
                                      return_tensors="pt").input_ids
        with torch.no_grad():
            outputs = model.generate(
                pixel_values.to(device),
                decoder_input_ids=decoder_input_ids.to(device),
                max_length=model.decoder.config.max_length,
                early_stopping=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                use_cache=True,
                num_beams=1,
                bad_words_ids=[[tokenizer.unk_token_id]],
                return_dict_in_generate=True,
            )
        sequence = tokenizer.batch_decode(outputs.sequences)[0]
        sequence = sequence.replace(tokenizer.eos_token, "").replace(tokenizer.pad_token, "").replace(tokenizer.bos_token, "")
        sequence = process_raw_latex_code(sequence)
        return sequence
    except Exception as e:
        print(f"Error processing {image_path}: {str(e)}")
        return None


def run_nougat_latex():
    args = parse_option()
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # device
    if args.device == "gpu":
        device = torch.device("cuda:0")
    else:
        device = torch.device("cpu")

    # init model
    print("Loading model...")
    model = VisionEncoderDecoderModel.from_pretrained(args.pretrained_model_name_or_path).to(device)

    # init processor
    tokenizer = NougatTokenizerFast.from_pretrained(args.pretrained_model_name_or_path)
    latex_processor = NougatLaTexProcessor.from_pretrained(args.pretrained_model_name_or_path)
    
    # Get image paths
    if os.path.isfile(args.img_path):
        # Single image
        image_paths = [args.img_path]
    elif os.path.isdir(args.img_path):
        # Directory of images
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
        image_paths = []
        for ext in image_extensions:
            image_paths.extend(glob.glob(os.path.join(args.img_path, ext)))
            image_paths.extend(glob.glob(os.path.join(args.img_path, ext.upper())))
        # Sort image paths to ensure consistent order
        image_paths.sort()
    else:
        print(f"Error: {args.img_path} is not a valid file or directory")
        return
    
    if not image_paths:
        print(f"No images found in {args.img_path}")
        return
    
    print(f"Found {len(image_paths)} images to process")
    print(f"Results will be saved to: {args.output_file}")
    
    # Open output file for writing all results
    with open(args.output_file, 'w', encoding='utf-8') as output_f:
        # Process each image
        for i, image_path in enumerate(image_paths, 1):
            print(f"Processing {i}/{len(image_paths)}: {os.path.basename(image_path)}")
            
            sequence = process_single_image(image_path, model, tokenizer, latex_processor, device)
            
            if sequence is not None:
                # Write result to the single output file
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                # output_f.write(f"Image: {base_name}\n")
                # output_f.write(f"LaTeX: {sequence}\n")
                # output_f.write("-" * 80 + "\n")
                output_f.write(sequence + "\n")
                
                print(f"LaTeX: {sequence}")
                print("-" * 50)
            else:
                print(f"Failed to process: {image_path}")
                # Write error info to output file
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_f.write(f"Image: {base_name}\n")
                output_f.write(f"LaTeX: ERROR - Failed to process\n")
                output_f.write("-" * 80 + "\n")
                print("-" * 50)
    
    print(f"\nProcessing completed! All results saved to: {args.output_file}")


if __name__ == '__main__':
    run_nougat_latex()
