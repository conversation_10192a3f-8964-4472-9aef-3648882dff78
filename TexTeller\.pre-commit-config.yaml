repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.6
    hooks:
      - id: ruff
        args: [--fix, --respect-gitignore, --config=pyproject.toml]
        exclude: ^texteller/models/thrid_party/paddleocr/
      - id: ruff-format
        args: [--config=pyproject.toml]
        exclude: ^texteller/models/thrid_party/paddleocr/

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
