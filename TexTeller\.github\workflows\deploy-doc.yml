name: "Sphinx: Render docs"

on: push

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Build HTML
      uses: ammaraskar/sphinx-action@7.0.0
      with:
        pre-build-command: |
          apt-get update && apt-get install -y git
          pip install uv
          uv pip install --system . .[docs]
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: html-docs
        path: docs/build/html/
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: docs/build/html
