"""
    Usage:  python tools/crop_formula_regions.py "json目录路径" "图像目录路径" "输出目录路径"
"""

import json
import os
from pathlib import Path
from PIL import Image
import argparse


def crop_formula_regions(json_dir: str, image_dir: str, output_dir: str):
    """
    从JSON文件中读取Formula区域信息，裁剪对应图像并保存
    
    Args:
        json_dir: JSON文件目录路径
        image_dir: 图像文件目录路径  
        output_dir: 裁剪后图像保存目录路径
    """
    json_dir = Path(json_dir)
    image_dir = Path(image_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有JSON文件
    json_files = list(json_dir.glob("*.json"))
    
    total_cropped = 0
    
    for json_file in json_files:
        # 获取对应的图像文件名
        base_name = json_file.stem  # 例如: DLF_000003
        
        # 查找对应的图像文件（支持常见图像格式）
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
        image_file = None
        
        for ext in image_extensions:
            potential_image = image_dir / f"{base_name}{ext}"
            if potential_image.exists():
                image_file = potential_image
                break
        
        if not image_file:
            print(f"⚠️  未找到对应的图像文件: {base_name}")
            continue
            
        # 读取JSON文件
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
        except Exception as e:
            print(f"❌ 读取JSON文件失败 {json_file}: {e}")
            continue
            
        # 打开图像
        try:
            image = Image.open(image_file)
        except Exception as e:
            print(f"❌ 打开图像文件失败 {image_file}: {e}")
            continue
            
        # 查找Formula区域并裁剪
        formula_count = 0
        
        for annotation in annotations:
            if annotation.get('class_name') == 'Formula':
                bbox = annotation.get('bbox')
                if not bbox or len(bbox) != 4:
                    print(f"⚠️  无效的bbox格式: {bbox}")
                    continue
                    
                x1, y1, x2, y2 = bbox
                
                # 确保坐标在图像范围内
                x1 = max(0, min(x1, image.width))
                y1 = max(0, min(y1, image.height))
                x2 = max(0, min(x2, image.width))
                y2 = max(0, min(y2, image.height))
                
                # 检查裁剪区域是否有效
                if x2 <= x1 or y2 <= y1:
                    print(f"⚠️  无效的裁剪区域: ({x1}, {y1}, {x2}, {y2})")
                    continue
                
                # 裁剪图像
                try:
                    cropped_image = image.crop((x1, y1, x2, y2))
                    
                    # 生成保存文件名
                    formula_count += 1
                    output_filename = f"{base_name}_{formula_count:02d}.png"
                    output_path = output_dir / output_filename
                    
                    # 保存裁剪后的图像
                    cropped_image.save(output_path, 'PNG')
                    total_cropped += 1
                    
                    print(f"✅ 已保存: {output_filename} (区域: {x1},{y1},{x2},{y2})")
                    
                except Exception as e:
                    print(f"❌ 裁剪保存失败: {e}")
                    continue
        
        if formula_count > 0:
            print(f"📄 {base_name}: 共裁剪 {formula_count} 个Formula区域")
    
    print(f"\n🎉 处理完成！总共裁剪保存了 {total_cropped} 个Formula区域")


def main():
    parser = argparse.ArgumentParser(description='从JSON标注文件中裁剪Formula区域')
    parser.add_argument('json_dir', help='JSON文件目录路径')
    parser.add_argument('image_dir', help='图像文件目录路径')
    parser.add_argument('output_dir', help='裁剪后图像保存目录路径')
    
    args = parser.parse_args()
    
    # 检查输入目录是否存在
    if not Path(args.json_dir).exists():
        print(f"❌ JSON目录不存在: {args.json_dir}")
        return
        
    if not Path(args.image_dir).exists():
        print(f"❌ 图像目录不存在: {args.image_dir}")
        return
    
    print(f"📂 JSON目录: {args.json_dir}")
    print(f"📂 图像目录: {args.image_dir}")
    print(f"📂 输出目录: {args.output_dir}")
    print("-" * 50)
    
    crop_formula_regions(args.json_dir, args.image_dir, args.output_dir)


if __name__ == '__main__':
    # main()

    json_dir = r"D:\results\DLA\cfv4\image1\recorrect_jsons"
    image_dir = r"D:\datasets\user_data_dlf\image1"
    output_dir = r"D:\results\DLA\cfv4\image1\crop_formula"
    crop_formula_regions(json_dir, image_dir, output_dir)
