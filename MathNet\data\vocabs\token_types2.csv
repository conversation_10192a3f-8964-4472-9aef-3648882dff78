,"0: Symbol, 1: bracket before, 2: bracket after, 3: remove token, 4: other, 5: two brackets, 6: remove formula",replace with,style
!,0,,n
',0,,n
(,0,,n
),0,,n
*,0,,n
+,0,,n
",",0,,n
-,0,,n
.,0,,n
/,0,,n
0,0,,n
1,0,,n
2,0,,n
3,0,,n
4,0,,n
5,0,,n
6,0,,n
7,0,,n
8,0,,n
9,0,,n
:,0,,n
;,0,,n
<,0,,n
=,0,,n
>,0,,n
A,0,,n
B,0,,n
C,0,,n
D,0,,n
E,0,,n
F,0,,n
G,0,,n
H,0,,n
I,0,,n
J,0,,n
K,0,,n
L,0,,n
M,0,,n
N,0,,n
O,0,,n
P,0,,n
Q,0,,n
R,0,,n
S,0,,n
T,0,,n
U,0,,n
V,0,,n
W,0,,n
X,0,,n
Y,0,,n
Z,0,,n
[,0,,n
\Big,3,,n
\Bigg,3,,n
\Biggr,3,,n
\Bigl,3,,n
\Bigr,3,,n
\Delta,0,,n
\Gamma,0,,n
\Lambda,0,,n
\Omega,0,,n
\Phi,0,,n
\Pi,0,,n
\Psi,0,,n
\Rightarrow,0,,n
\Sigma,0,,n
\Theta,0,,n
\Xi,0,,n
\alpha,0,,n
\approx,0,,n
\ast,0,,n
\bar,2,,n
\beta,0,,n
\bf,3,,n
\big,3,,n
\bigg,3,,n
\biggl,3,,n
\biggr,3,,n
\bigl,3,,n
\bigr,3,,n
\binom,5,,n
\boldmath,3,,n
\bot,0,,n
\cal,3,,n
\cdot,0,,n
\cdots,0,,n
\check,2,,n
\chi,0,,n
\circ,0,,n
\dag,0,,n
\dagger,0,,n
\ddot,2,,n
\delta,0,,n
\dot,2,,n
\dots,0,,n
\ell,0,,n
\epsilon,0,,n
\equiv,0,,n
\eta,0,,n
\forall,0,,n
\frac,5,,n
\gamma,0,,n
\ge,0,,n
\geq,0,\ge,n
\hat,2,,n
\hbar,2,,n
\hspace,3,,n
\imath,0,i,n
\in,0,,n
\infty,0,,n
\int,0,,n
\iota,0,,n
\it,3,,n
\jmath,0,j,n
\kappa,0,,n
\l,0,l,n
\lambda,0,,n
\langle,0,,n
\lbrack,0,[,n
\ldots,0,\dots,n
\le,0,,n
\leftrightarrow,0,,n
\leq,0,\le,n
\ll,0,,n
\longrightarrow,0,,n
\mapsto,0,,n
\mathbf,3,,n
\mathcal,2,,y
\mathrm,3,,n
\mid,0,|,n
\mp,0,,n
\mu,0,,n
\nabla,0,,n
\ne,0,,n
\neq,0,,n
\not,0,,n
\nu,0,,n
\oint,0,,n
\omega,0,,n
\operatorname,3,,n
\operatorname*,3,\operatorname,n
\oplus,0,,n
\otimes,0,,n
\overline,2,\bar,n
\overrightarrow,2,,n
\parallel,0,,n
\partial,0,,n
\perp,0,,n
\phi,0,,n
\pi,0,,n
\pm,0,,n
\prime,0,,n
\prod,0,,n
\propto,0,,n
\psi,0,,n
\rangle,0,,n
\rho,0,,n
\rightarrow,0,,n
\scriptsize,3,,n
\sigma,0,,n
\sim,0,,n
\simeq,0,,n
\sp,2,^,n
\sqrt,2,,n
\stackrel,5,,n
\star,0,^ { * },n
\subset,0,,n
\sum,0,,n
\tau,0,,n
\textrm,3,,n
\theta,0,,n
\tilde,2,,n
\times,0,,n
\to,0,,n
\triangle,0,,n
\underline,2,,n
\varepsilon,0,,n
\varphi,0,,n
\varrho,0,,n
\vartheta,0,,n
\vec,2,,n
\vert,0,|,n
\wedge,0,,n
\widehat,2,,n
\widetilde,2,,n
\xi,0,,n
\zeta,0,,n
\{,0,,n
\|,0,\parallel,n
\},0,,n
],0,,n
^,2,,n
_,2,,n
_END_,4,,n
_PAD_,4,,n
_START_,4,,n
_UNK_,4,,n
a,0,,n
b,0,,n
c,0,,n
d,0,,n
e,0,,n
f,0,,n
g,0,,n
h,0,,n
i,0,,n
j,0,,n
k,0,,n
l,0,,n
m,0,,n
n,0,,n
o,0,,n
p,0,,n
q,0,,n
r,0,,n
s,0,,n
t,0,,n
u,0,,n
v,0,,n
w,0,,n
x,0,,n
y,0,,n
z,0,,n
{,4,,n
|,0,,n
},4,,n
"\,",3,,n
\;,3,,n
\:,3,,n
~,3,,n
\nonumber,3,,n
\displaystyle,3,,n
\scriptstyle,3,,n
\scriptscriptstyle,3,,n
\textstyle,3,,n
\right,3,,n
\left,3,,n
\,3,,n
\-,3,,n
\!,3,,n
\quad,3,,n
\qquad,3,,n
\hfil,3,,n
\hfill,3,,n
\hfilll,3,,n
\vfil,3,,n
\vfill,3,,n
\vfilll,3,,n
. . .,0,\dots,n
?,0,,n
\#,0,,n
\&,0,,n
\',2,,n
\*,3,,n
\AA,0,,n
\Biggl,3,,n
\Biggm,3,,n
\Bigm,3,,n
\Huge,3,,n
\Im,0,,n
\L,0,,n
\LARGE,3,,n
\Large,3,,n
\Leftrightarrow,0,,n
\Longleftarrow,0,,n
\Longleftrightarrow,0,,n
\Longrightarrow,0,,n
\O,0,,n
\P,0,,n
\Re,0,,n
\S,0,,n
\SS,0,S S,n
\Upsilon,0,,n
\Vert,0,\parallel,n
\^,2,\hat,n
\_,0,,n
\acute,2,,n
\ae,0,,n
\aleph,0,,n
\amalg,0,,n
\arrowvert,0,|,n
\asymp,0,,n
\atop,1,,n
\atopwithdelims,1,\atop,n
\b,0,,n
\backslash,0,,n
\bigcap,0,,n
\bigcup,0,,n
\biggm,3,,n
\bigm,3,,n
\bigoplus,0,,n
\bigotimes,0,,n
\bigsqcup,0,,n
\bigtriangledown,0,,n
\bigtriangleup,0,,n
\bigvee,0,,n
\bigwedge,0,,n
\bmod,0,,n
\brace,1,,n
\brack,1,,n
\breve,2,,n
\buildrel,1,,n
\bullet,0,,n
\c,0,,n
\cap,0,,n
\cdotp,0,,n
\cite,6,,n
\colon,0,:,n
\cong,0,,n
\crcr,6,,n
\cup,0,,n
\d,0,,n
\ddag,0,,n
\ddagger,0,\ddag,n
\diamond,0,,n
\diamondsuit,0,,n
\do,6,,n
\doteq,0,,n
\downarrow,0,,n
\em,3,,n
\emptyset,0,,n
\enskip,3,,n
\enspace,3,,n
\ensuremath,3,,n
\exists,0,,n
\fbox,6,,n
\fboxsep,6,,n
\fill,3,,n
\flat,0,,n
\footnotemark,6,,n
\footnotesize,6,,n
\framebox,6,,n
\gg,0,,n
\grave,2,,n
\hookrightarrow,0,,n
\hphantom,6,,n
\hrule,0,,n
\hss,3,,n
\i,0,,n
\itshape,3,,n
\j,0,,n
\kern,3,,n
\land,0,,n
\large,3,,n
\lbrace,0,\{,n
\lceil,0,,n
\leavevmode,3,,n
\leftarrow,0,,n
\lefteqn,3,,n
\lfloor,0,,n
\lgroup,0,,n
\llap,3,,n
\longleftarrow,0,,n
\longleftrightarrow,0,,n
\longmapsto,0,,n
\lower,6,,n
\lq,0,,n
\makebox,6,,n
\mathaccent,6,,n
\mathbin,3,,n
\mathclose,6,,n
\mathit,3,,n
\mathop,3,,n
\mathopen,6,,n
\mathrel,3,,n
\mathsf,3,,n
\mathstrut,3,,n
\medskip,3,,n
\mit,3,\mathit,n
\mkern,3,,n
\mskip,3,,n
\natural,0,,n
\nearrow,0,,n
\ni,0,,n
\normalsize,3,,n
\notin,0,,n
\null,3,,n
\nulldelimiterspace,3,,n
\o,0,,n
\odot,0,,n
\of,6,,n
\ominus,0,,n
\ooalign,3,,n
\overbrace,2,,n
\overleftarrow,2,,n
\overwithdelims,2,,n
\parbox,6,,n
\phantom,6,\hphantom,n
\pmod,2,,n
\pounds,0,,n
\prec,0,,n
\protect,3,,n
\protectE,3,,n
\protectZ,3,,n
\protecte,3,,n
\protectm,3,,n
\protectu,3,,n
\raise,6,,n
\raisebox,6,,n
\rbrace,0,\},n
\rbrack,0,,n
\rceil,0,,n
\ref,6,,n
\relax,6,,n
\rfloor,0,,n
\rgroup,0,,n
\rightarrowfill,0,\rightarrow,n
\rightharpoonup,0,,n
\rightleftharpoons,0,,n
\rlap,6	,,n
\root,6,,n
\sb,2,_,n
\sc,3,,n
\searrow,0,,n
\setcounter,6,,n
\setlength,6,,n
\setminus,0,\backslash,n
\sf,3,,n
\sharp,0,,n
\skew,6,,n
\sl,3,,n
\slash,0,,n
\small,3,,n
\smallskip,3,,n
\smash,3,,n
\smile,6,,n
\space,3,,n
\special,6,,n
\sqcap,0,,n
\sqcup,0,,n
\ss,0,,n
\strut,3,,n
\subseteq,0,,n
\succeq,0,,n
\supset,0,,n
\supseteq,0,,n
\surd,0,,n
\symbol,0,,n
\textbf,3,,n
\textit,3,,n
\textnormal,3,,n
\textsf,3,,n
\texttt,3,,n
\textup,3,,n
\thinspace,3,,n
\tiny,3,,n
\top,0,,n
\triangleleft,0,,n
\triangleright,0,,n
\tt,3,,n
\unboldmath,3,,n
\underbrace,2,,n
\uparrow,0,,n
\upsilon,0,,n
\varpi,0,,n
\varsigma,0,,n
\vcenter,3,,n
\vdash,0,,n
\vdots,0,,n
\vee,0,,n
\vline,0,|,n
\vphantom,6,,n
\vrule,0,|,n
\vss,3,,n
\wp,0,,n
`,0,,n
\begin{array},2,,n
\end{array},0,,n
&,0,,n
\vskip,6,,n
\renewcommand,6,,n
a r g,0,\arg,n
m i n,0,\min,n
m a x,0,\max,n
l o g,0,\log,n
l n,0,\ln,n
c o s h,0,\cosh,n
c o s,0,\cos,n
s i n h,0,\sinh,n
s i n,0,\sin,n
\boldsymbol,2,,y
\mathbb,2,,y
\sb,0,_,n
\sp,0,^,n
\lt,0,<,n
\gt,0,>,n