arguments:
  config: configs/im2latexv2-cvt.yaml
  image_path: null
  num_workers: null
  resume_from: null
  sample_size: null
  task:
  - train
  test_set: null
  turn_off_wandb: false
dataset:
  dpi: 600
  test:
    batch_size: 36
    cache: false
    drop_last: false
    files:
      im2latex:
      - config-im2latex-kaggle.yaml
      im2latex600:
      - config-im2latex-kaggle-600.yaml
      im2latexv2:
      - dataset-im2latexv2.yaml
    no_arrays: false
    no_sampling: false
    normalize: false
    num_workers: 15
    only_basic: false
    shuffle: false
    transforms:
    - WhiteBorder
    - White2Black
  train:
    batch_size: 36
    cache: false
    drop_last: true
    file:
    - dataset-im2latexv2.yaml
    no_arrays: false
    no_sampling: false
    normalize: false
    num_workers: 15
    only_basic: false
    shuffle: true
    transforms:
    - AddWhitespace
    - RandomResize
    - WhiteBorder
    - DownUpResize
    - GaussianBlur
    - ColorJitter
    - White2Black
  val:
    batch_size: 36
    cache: false
    drop_last: false
    file:
    - dataset-im2latexv2.yaml
    no_arrays: false
    no_sampling: false
    normalize: false
    num_workers: 15
    only_basic: true
    shuffle: false
    transforms:
    - WhiteBorder
    - White2Black
  vocab_file: data/vocabs/im2latexv2.vocab
device: cuda
model:
  counting: false
  cnn:
    deformable_cnn: []
    in_channels: 1
    kernel_size: 3
    num_layers: 5
    padding: 1
    stride: 1
  cvt:
    dropout: 0.0
    s1_depth: 1
    s1_emb_dim: 64
    s1_emb_kernel: 48
    s1_emb_stride: 12
    s1_heads: 1
    s1_kv_proj_stride: 2
    s1_mlp_mult: 4
    s1_proj_kernel: 3
    s2_depth: 2
    s2_emb_dim: 192
    s2_emb_kernel: 3
    s2_emb_stride: 2
    s2_heads: 3
    s2_kv_proj_stride: 2
    s2_mlp_mult: 4
    s2_proj_kernel: 3
    s3_depth: 10
    s3_emb_kernel: 3
    s3_emb_stride: 2
    s3_heads: 4
    s3_kv_proj_stride: 2
    s3_mlp_mult: 4
    s3_proj_kernel: 3
  decoder: transformer
  encoder: CvT
  encoder_emb: 384
  image:
    height: 768
    width: 2400
  max_len: 150
  model_save_path: checkpoints/run_2023-3-31_12-52-19
  softmax: true
  transformer:
    dropout: 0.1
    hidden_size: 512
    n_head: 8
    num_decoder_layers: 4
  vit:
    arch: vit_tiny_math
    channels: 1
    depth: 1
    dim: 512
    dropout: 0
    emb_dropout: 0
    heads: 2
    mlp_dim: 1024
    patch_size: 16
  vocab_size: 326
  wav2vec2:
    config_file: configs/pretrain-wav2vec.yaml
    model: checkpoints/wav2vec/model/epoch_25.pt
test:
  each: 10
  more_information: true
train:
  criterion: CrossEntropyLoss
  early_stop: 200
  epochs: 200
  fp16: true
  freeze_encoder: -1
  freeze_feature_extractor: -1
  grad_clip_value: null
  init_lr: 7.500000000000001e-05
  lr_descent:
  - 10000
  - 1
  predict_style: false
  save_each: 5
  style_loss_factor: 1
  wait_n_epochs: 30
val:
  each: 5
  metric: Edit
  more_information: true
wandb:
  group: im2latexv2
  log_gradients: false
  n_errors: 10
  name: im2latexv2-cvt-moreWhitespace-2023-3-31_12-52-19
  project: Formula Recognition
  table: true
  tags:
  - im2latexv2
  - vit
  train_image: null
  use: true
