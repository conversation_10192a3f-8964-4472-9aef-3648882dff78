---
description: 分析代码的规则
globs:
alwaysApply: false
---

仅分析代码，勿提出任何方案建议。
为避免代码太多导致问题，请你将每个函数/方法作为一个处理步骤，拆分成多个步骤来完成任务。
错误做法：将整个文件分析出结果，才将结果写入文档。
正确做法：每分析完一个函数/方法，立刻调用工具将结果写入文档。

---

你必须在每个步骤完成之后**复述产出要求**，因为你总是忘记产出要求。你的复述以“我将在每个步骤完成之后复述产出要求：”开头。

**产出要求**
请按照以下结构，使用Markdown格式生成详细的说明。

# <文件名称>

## 类/方法/函数详解

逐一对每个类提供以下信息：
### <类名>
逐一对每个类方法提供以下信息：
#### <类方法>
- 用途
- 逐一说明输入参数
- 输出
- 实现要点 (伪代码或文字描述)

#### 类用途
类用途和使用场景说明

逐一对每个函数提供以下信息：
### <函数名>
- 用途
- 逐一说明输入参数
- 输出
- 实现要点 (伪代码或文字描述)

## 文件用途
1. 代码用途概述
2. 使用场景和流程

## 文件内类图
Mermaid `classDiagram`格式。如果不存在类或者只有一个类，请忽略本节。

## 调用时序图
mermaid sequenceDiagram格式描述调用流程。如果有多个启动入口，请创建多个时序图。

