---
type: "manual"
---

## 渐进式添加新功能指导原则

在添加新功能的过程中，请严格遵循以下指导原则：

1. **功能识别与模块化**：
  - 明确识别新功能所涉及的**核心业务逻辑**和**用户交互点**。
  - 基于新功能的职责和逻辑边界，进行**模块化设计**，以便于未来的维护和扩展。
  - **优先复用现有功能**和模块，避免重复造轮子。对于需要修改或扩展的现有模块，请评估其对新功能的影响。

2. **渐进式功能开发与集成**：
  - 将新功能分解为**可管理的、小步骤的开发任务**。每个小步骤都应能独立完成并进行验证。
  - **关键要求**：每完成一小步开发后，必须确保整个应用程序能够成功启动并保持可运行状态。同时，应用程序应能（部分地）展示出由这一小步开发所带来的新功能效果或变化。
  - 在开始对新功能进行实际开发前，请简要说明你计划采取的**渐进式开发步骤**，包括如何逐步集成到现有系统中。

3. **采用分层的模块化策略**：
  - 在添加新功能时，请注意进行**适度的模块化处理**。确保新添加的每个代码文件不超过**500行**。
  - 避免过度细化，在满足行数限制的前提下，避免将文件或模块拆分得过小或功能过于琐碎。力求使每个模块/文件承载相对完整且有意义的功能。
  - 将不同模块归纳到不同的层。将新功能涉及的业务逻辑、数据访问和用户界面等部分归纳到各自的层级，保持清晰的职责分离。将程序入口点视为最顶层。

4. **执行计划与确认**：
  - 在正式开始任何代码修改之前，请先提交一份你的**新功能开发计划**。这份计划应包括：
    * 对新功能的**初步分解**和**模块化设想**。
    * 对**受影响的现有模块**以及可能进行的**适配或扩展**的说明。
    * 你计划的**渐进式开发与集成步骤**。每个步骤的应包括参考信息，如codebase中已有代码、其它API等等。
  - 我将在审阅此计划后，给予你执行的确认或调整建议。

请确保你的每一步操作都清晰、有条理，并优先保证代码在功能添加过程中的**稳定性和可验证性**。