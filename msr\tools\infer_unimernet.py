import os
import time
import torch
from PIL import Image
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
import unimernet

if __name__ == "__main__":
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    model_path = r"models/converted/unimernet_base"
    model = VisionEncoderDecoderModel.from_pretrained(model_path).to(device)
    processor = TrOCRProcessor.from_pretrained(model_path, use_fast=True)
    # processor = TrOCRProcessor.from_pretrained(model_path)
    # print(processor)

    root_path = r""
    img_list = os.listdir(root_path)
    img_list.sort()

    out_txt = []

    for i in range(1):
        start_time = time.time()
        # image_fps = [
        #     'test_imgs/0000004.png',
        #     'test_imgs/0000006.png',
        #     'test_imgs/0000007.png',
        #     'test_imgs/0000010.png',
        # ]
        images = [Image.open(os.path.join(root_path, imname)).convert('RGB') for imname in img_list]

        pixel_values = processor(
            images=images, return_tensors="pt").pixel_values.to(device)
        generated_ids = model.generate(pixel_values)
        generated_text = processor.batch_decode(
            generated_ids, skip_special_tokens=True)

        for (img, text) in zip(images, generated_text):
            out_txt.append(text)
            print(text)

        end_time = time.time()
        print("time taken:", (end_time - start_time)*1000)
    
    with open("pred_unimernet_base.txt", "w") as f:
        f.write("\n".join(out_txt))

