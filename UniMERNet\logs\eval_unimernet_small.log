CustomVisionEncoderDecoderModel init
VariableUnimerNetModel init
VariableUnimerNetPatchEmbeddings init
VariableUnimerNetModel init
VariableUnimerNetPatchEmbeddings init
CustomMBartForCausalLM init
CustomMBartDecoder init
arch_name:unimernet
model_type:unimernet
checkpoint:
====================================================================================================
Device:cuda
Load model: 14.039s
len_gts:6762, len_preds=6762
norm_gts[0]:S\sim\tilde{\psi}Q_{o}\tilde{\psi}+g_{s}^{1/2}\tilde{\psi}^{3}+\tilde{\phi}Q_{c}\tilde{\phi}+g_{s}\tilde{\phi}^{3}+\tilde{\phi}B(g_{s}^{1/2}\tilde{\psi})+\cdots.
norm_preds[0]:S\sim\tilde{\psi}Q_{o}\tilde{\psi}+g_{s}^{1/2}\tilde{\psi}^{3}+\tilde{\phi}Q_{c}\tilde{\phi}+g_{s}\tilde{\phi}^{3}+\tilde{\phi}B(g_{s}^{1/2}\tilde{\psi})+\cdots.
Evaluation Set:Simple Print Expression(SPE)
Inference Time: 489.57221508026123s
  bleu ⬆    meteor ⬆     edit ⬇
--------  ----------  ---------
0.913463     0.89984  0.0614923
====================================================================================================
len_gts:5921, len_preds=5921
norm_gts[0]:\begin{array}{r l}{\mathcal{L}(\{\mathbf{u,v,w,z,x}\},\{\boldsymbol{\kappa,\lambda,\mu,\nu}\})=\frac{1}{2}\|\mathbf{y-Cu}\|_{2}^{2}}&{+\tau_{1}\|\mathbf{v}\|_{1}+\tau_{2}\|\mathbf{w}\|_{1}}\\ &{+\frac{\rho_{1}}{2}\|\mathbf{Ax-u}\|_{2}^{2}+\boldsymbol{\kappa}^{\top}(\mathbf{Ax-u})}\\ &{+\frac{\rho_{2}}{2}\|\mathbf{x-v}\|_{2}^{2}+\boldsymbol{\lambda}^{\top}(\mathbf{x-v})}\\ &{+\frac{\rho_{3}}{2}\|\mathbf{Dx-w}\|_{2}^{2}+\boldsymbol{\mu}^{\top}(\mathbf{Dx-w})}\\ &{+\frac{\rho_{4}}{2}\|\mathbf{x-z}\|_{2}^{2}+\boldsymbol{\nu}^{\top}(\mathbf{x-z})}\\ &{+\mathcal{I}_{+}(\mathbf{z})}\end{array}
norm_preds[0]:\begin{array}{r l}{\mathcal{L}(\{\mathbf{u,v,w,z,x}\},\{\kappa,\lambda,\mu,\nu\})=\frac{1}{2}\|\mathbf{y-Cu}\|_{2}^{2}}&{+\tau_{1}\|\mathbf{v}\|_{1}+\tau_{2}\|\mathbf{w}\|_{1}}\\ &{+\frac{\rho_{1}}{2}\|\mathbf{Ax-u}\|_{2}^{2}+\kappa^{\top}(\mathbf{Ax-u})}\\ &{+\frac{\rho_{2}}{2}\|\mathbf{x-v}\|_{2}^{2}+\lambda^{\top}(\mathbf{x-v})}\\ &{+\frac{\rho_{3}}{2}\|\mathbf{Dx-w}\|_{2}^{2}+\mu^{\top}(\mathbf{Dx-w})}\\ &{+\frac{\rho_{4}}{2}\|\mathbf{x-z}\|_{2}^{2}+\nu^{\top}(\mathbf{x-z})}\\ &{+\mathcal{I}_{+}(\mathbf{z})}\end{array}
Evaluation Set:Complex Print Expression(CPE)
Inference Time: 1294.0490927696228s
  bleu ⬆    meteor ⬆     edit ⬇
--------  ----------  ---------
0.919805    0.895851  0.0597243
====================================================================================================
len_gts:4742, len_preds=4742
norm_gts[0]:F_{i}[z](x,y)=f_{i}(x,y,z)\ i=1,\ldots,n,
norm_preds[0]:F_{i}[z](x,y)=f_{i}(x,y,z)~i=1,\ldots,n,
Evaluation Set:Screen Capture Expression(SCE)
Inference Time: 1570.1024556159973s
  bleu ⬆    meteor ⬆    edit ⬇
--------  ----------  --------
0.617545    0.678047   0.22849
====================================================================================================
len_gts:6332, len_preds=6332
norm_gts[0]:b_{n+1}-b_{n}=-1
norm_preds[0]:b_{n+1}-b_{n}=-1
Evaluation Set:Handwritten Expression(HWE)
Inference Time: 1641.9185965061188s
  bleu ⬆    meteor ⬆     edit ⬇
--------  ----------  ---------
0.889342    0.850023  0.0748587
================================